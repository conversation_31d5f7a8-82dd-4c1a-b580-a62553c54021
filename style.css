/* Custom styles for Live Translate */

/* Microphone button animations */
.mic-recording {
    animation: pulse 1.5s infinite;
    background-color: #DC2626 !important;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(220, 38, 38, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
    }
}

/* Status indicator animations */
.status-listening {
    background-color: #FEF3C7 !important;
    color: #92400E !important;
}

.status-listening .status-dot {
    background-color: #F59E0B !important;
    animation: blink 1s infinite;
}

.status-translating {
    background-color: #DBEAFE !important;
    color: #1E40AF !important;
}

.status-translating .status-dot {
    background-color: #3B82F6 !important;
    animation: spin 1s linear infinite;
}

.status-ready {
    background-color: #F3F4F6 !important;
    color: #6B7280 !important;
}

.status-ready .status-dot {
    background-color: #9CA3AF !important;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .mic-button {
        width: 4rem;
        height: 4rem;
    }
    
    .mic-button svg {
        width: 1.5rem;
        height: 1.5rem;
    }
    
    .conversation-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .speech-display, .translation-display {
        min-height: 80px;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
button:focus,
input:focus,
select:focus {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .bg-gray-50 {
        background-color: #FFFFFF;
    }
    
    .bg-blue-50 {
        background-color: #EFF6FF;
        border: 1px solid #3B82F6;
    }
    
    .bg-green-50 {
        background-color: #F0FDF4;
        border: 1px solid #10B981;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .mic-recording {
        animation: none;
    }
    
    .status-listening .status-dot,
    .status-translating .status-dot {
        animation: none;
    }
    
    * {
        transition: none !important;
    }
}

/* Print styles */
@media print {
    .mic-button,
    button {
        display: none;
    }
    
    .speech-display,
    .translation-display {
        border: 1px solid #000;
    }
}

/* Loading spinner for translation */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3B82F6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Error states */
.error-state {
    background-color: #FEF2F2 !important;
    border-color: #F87171 !important;
    color: #991B1B !important;
}

/* Success states */
.success-state {
    background-color: #F0FDF4 !important;
    border-color: #34D399 !important;
    color: #065F46 !important;
}

/* Custom scrollbar for better mobile experience */
.speech-display::-webkit-scrollbar,
.translation-display::-webkit-scrollbar {
    width: 4px;
}

.speech-display::-webkit-scrollbar-track,
.translation-display::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.speech-display::-webkit-scrollbar-thumb,
.translation-display::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.speech-display::-webkit-scrollbar-thumb:hover,
.translation-display::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
