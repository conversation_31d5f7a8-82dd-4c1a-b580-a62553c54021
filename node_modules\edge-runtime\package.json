{"name": "edge-runtime", "description": "Run any Edge Function from CLI or Node.js module.", "homepage": "https://edge-runtime.vercel.app/packages/runtime", "version": "2.5.7", "main": "dist/index.js", "bin": {"edge-runtime": "dist/cli/index.js"}, "repository": {"directory": "packages/runtime", "type": "git", "url": "git+https://github.com/vercel/edge-runtime.git"}, "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "keywords": ["edge", "edge-runtime", "functions", "runtime", "standard", "web"], "dependencies": {"async-listen": "3.0.1", "mri": "1.2.0", "picocolors": "1.0.0", "pretty-ms": "7.0.1", "signal-exit": "4.0.2", "time-span": "4.0.0", "@edge-runtime/format": "2.2.0", "@edge-runtime/ponyfill": "2.4.1", "@edge-runtime/vm": "3.1.7"}, "devDependencies": {"@types/node-fetch": "2", "node-fetch": "2"}, "engines": {"node": ">=16"}, "files": ["dist"], "license": "MPL-2.0", "publishConfig": {"access": "public"}, "types": "dist/index.d.ts", "scripts": {"build": "tsc --project tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:build": "rm -rf dist", "clean:node": "rm -rf node_modules", "postversion": "pnpm run build", "prebuild": "pnpm run clean:build", "test": "jest"}}