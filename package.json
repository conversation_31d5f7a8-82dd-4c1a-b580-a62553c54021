{"name": "live-translate", "version": "1.0.0", "description": "Conversational live translation web application with voice-to-voice translation", "main": "index.html", "scripts": {"dev": "node server.js", "build": "echo 'No build step required for static files'", "start": "node server.js", "deploy": "vercel --prod"}, "keywords": ["translation", "voice", "conversation", "openai", "speech-recognition", "text-to-speech"], "author": "Live Translate", "license": "MIT", "dependencies": {"@gradio/client": "^1.15.2", "dotenv": "^16.5.0"}, "devDependencies": {"vercel": "^32.0.0"}, "engines": {"node": ">=18.0.0"}}