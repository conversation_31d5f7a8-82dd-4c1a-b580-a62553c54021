<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Translate - Test Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">Live Translate - Test Suite</h1>
        
        <!-- API Test Section -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">API Tests</h2>
            
            <!-- Authentication Test -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-700 mb-2">Authentication Test</h3>
                <div class="flex gap-4 items-center mb-2">
                    <input type="password" id="testKey" placeholder="Enter admin key" class="px-3 py-2 border rounded-lg">
                    <button id="testAuth" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">Test Auth</button>
                </div>
                <div id="authResult" class="text-sm"></div>
            </div>
            
            <!-- Translation Test -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-700 mb-2">Translation Test</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-2">
                    <input type="text" id="testText" placeholder="Text to translate" class="px-3 py-2 border rounded-lg">
                    <select id="testSourceLang" class="px-3 py-2 border rounded-lg">
                        <option value="tr" selected>Turkish</option>
                        <option value="nl">Dutch</option>
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                    </select>
                    <select id="testTargetLang" class="px-3 py-2 border rounded-lg">
                        <option value="nl" selected>Dutch</option>
                        <option value="tr">Turkish</option>
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                    </select>
                </div>
                <button id="testTranslate" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 mb-2">Test Translation</button>
                <div id="translateResult" class="text-sm"></div>
            </div>
        </div>
        
        <!-- Browser Compatibility Test -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Browser Compatibility</h2>
            <div id="compatibilityResults" class="space-y-2"></div>
        </div>
        
        <!-- Speech API Test -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Speech API Tests</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-medium text-gray-700 mb-2">Speech Recognition</h3>
                    <button id="testSpeechRecognition" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 mb-2">Test Recognition</button>
                    <div id="speechRecognitionResult" class="text-sm"></div>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-700 mb-2">Speech Synthesis</h3>
                    <div class="flex gap-2 mb-2">
                        <input type="text" id="testSpeechText" placeholder="Text to speak" class="px-3 py-2 border rounded-lg flex-1">
                        <button id="testSpeechSynthesis" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700">Speak</button>
                    </div>
                    <div id="speechSynthesisResult" class="text-sm"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test suite for Live Translate
        class LiveTranslateTest {
            constructor() {
                this.init();
            }
            
            init() {
                this.setupEventListeners();
                this.runCompatibilityTests();
            }
            
            setupEventListeners() {
                document.getElementById('testAuth').addEventListener('click', () => this.testAuthentication());
                document.getElementById('testTranslate').addEventListener('click', () => this.testTranslation());
                document.getElementById('testSpeechRecognition').addEventListener('click', () => this.testSpeechRecognition());
                document.getElementById('testSpeechSynthesis').addEventListener('click', () => this.testSpeechSynthesis());
            }
            
            async testAuthentication() {
                const key = document.getElementById('testKey').value;
                const resultDiv = document.getElementById('authResult');
                
                if (!key) {
                    resultDiv.innerHTML = '<span class="text-red-600">Please enter an admin key</span>';
                    return;
                }
                
                try {
                    const response = await fetch('/api/validate-key', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key })
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        resultDiv.innerHTML = '<span class="text-green-600">✓ Authentication successful</span>';
                    } else {
                        resultDiv.innerHTML = `<span class="text-red-600">✗ ${result.error}</span>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<span class="text-red-600">✗ Error: ${error.message}</span>`;
                }
            }
            
            async testTranslation() {
                const text = document.getElementById('testText').value;
                const sourceLang = document.getElementById('testSourceLang').value;
                const targetLang = document.getElementById('testTargetLang').value;
                const resultDiv = document.getElementById('translateResult');
                
                if (!text) {
                    resultDiv.innerHTML = '<span class="text-red-600">Please enter text to translate</span>';
                    return;
                }
                
                resultDiv.innerHTML = '<span class="text-blue-600">Translating...</span>';
                
                try {
                    const response = await fetch('/api/translate', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text, sourceLang, targetLang })
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        resultDiv.innerHTML = `<span class="text-green-600">✓ Translation: "${result.translation}"</span>`;
                    } else {
                        resultDiv.innerHTML = `<span class="text-red-600">✗ ${result.error}</span>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<span class="text-red-600">✗ Error: ${error.message}</span>`;
                }
            }
            
            testSpeechRecognition() {
                const resultDiv = document.getElementById('speechRecognitionResult');
                
                if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                    resultDiv.innerHTML = '<span class="text-green-600">✓ Speech Recognition API supported</span>';
                } else {
                    resultDiv.innerHTML = '<span class="text-red-600">✗ Speech Recognition API not supported</span>';
                }
            }
            
            testSpeechSynthesis() {
                const text = document.getElementById('testSpeechText').value || 'Hello, this is a test';
                const resultDiv = document.getElementById('speechSynthesisResult');
                
                if ('speechSynthesis' in window) {
                    try {
                        const utterance = new SpeechSynthesisUtterance(text);
                        speechSynthesis.speak(utterance);
                        resultDiv.innerHTML = '<span class="text-green-600">✓ Speech Synthesis working</span>';
                    } catch (error) {
                        resultDiv.innerHTML = `<span class="text-red-600">✗ Error: ${error.message}</span>`;
                    }
                } else {
                    resultDiv.innerHTML = '<span class="text-red-600">✗ Speech Synthesis API not supported</span>';
                }
            }
            
            runCompatibilityTests() {
                const tests = [
                    { name: 'localStorage', test: () => typeof Storage !== 'undefined' },
                    { name: 'Fetch API', test: () => typeof fetch !== 'undefined' },
                    { name: 'Web Speech API', test: () => 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window },
                    { name: 'Speech Synthesis', test: () => 'speechSynthesis' in window },
                    { name: 'Microphone Access', test: () => navigator.mediaDevices && navigator.mediaDevices.getUserMedia },
                    { name: 'ES6 Support', test: () => typeof Promise !== 'undefined' && typeof fetch !== 'undefined' }
                ];
                
                const resultsDiv = document.getElementById('compatibilityResults');
                
                tests.forEach(test => {
                    const isSupported = test.test();
                    const statusClass = isSupported ? 'text-green-600' : 'text-red-600';
                    const statusIcon = isSupported ? '✓' : '✗';
                    
                    resultsDiv.innerHTML += `<div><span class="${statusClass}">${statusIcon} ${test.name}</span></div>`;
                });
            }
        }
        
        // Initialize test suite
        document.addEventListener('DOMContentLoaded', () => {
            new LiveTranslateTest();
        });
    </script>
</body>
</html>
