<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Translate - Test Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">Live Translate - Test Suite</h1>
        
        <!-- API Test Section -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">API Tests</h2>
            
            <!-- Authentication Test -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-700 mb-2">Authentication Test</h3>
                <div class="flex gap-4 items-center mb-2">
                    <input type="password" id="testKey" placeholder="Enter admin key" class="px-3 py-2 border rounded-lg">
                    <button id="testAuth" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">Test Auth</button>
                </div>
                <div id="authResult" class="text-sm"></div>
            </div>
            
            <!-- Translation Test -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-700 mb-2">Translation Test</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-2">
                    <input type="text" id="testText" placeholder="Text to translate" class="px-3 py-2 border rounded-lg">
                    <select id="testSourceLang" class="px-3 py-2 border rounded-lg">
                        <option value="tr" selected>Turkish</option>
                        <option value="nl">Dutch</option>
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                    </select>
                    <select id="testTargetLang" class="px-3 py-2 border rounded-lg">
                        <option value="nl" selected>Dutch</option>
                        <option value="tr">Turkish</option>
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                    </select>
                </div>

                <!-- Turkish Test Sentences -->
                <div class="mb-4">
                    <h4 class="text-md font-medium text-gray-600 mb-2">Quick Turkish Test Sentences:</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        <button class="turkish-test-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded text-sm text-left" data-text="Merhaba, nasılsınız?">
                            Merhaba, nasılsınız?
                        </button>
                        <button class="turkish-test-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded text-sm text-left" data-text="Bu çok güzel bir gün.">
                            Bu çok güzel bir gün.
                        </button>
                        <button class="turkish-test-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded text-sm text-left" data-text="Yardımınıza ihtiyacım var.">
                            Yardımınıza ihtiyacım var.
                        </button>
                        <button class="turkish-test-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded text-sm text-left" data-text="Restoran nerede?">
                            Restoran nerede?
                        </button>
                        <button class="turkish-test-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded text-sm text-left" data-text="Teşekkür ederim, çok naziksiniz.">
                            Teşekkür ederim, çok naziksiniz.
                        </button>
                        <button class="turkish-test-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded text-sm text-left" data-text="Havaalanına nasıl gidebilirim?">
                            Havaalanına nasıl gidebilirim?
                        </button>
                        <button class="turkish-test-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded text-sm text-left" data-text="Bu ne kadar tutuyor?">
                            Bu ne kadar tutuyor?
                        </button>
                        <button class="turkish-test-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded text-sm text-left" data-text="Özür dilerim, anlamadım.">
                            Özür dilerim, anlamadım.
                        </button>
                        <button class="turkish-test-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded text-sm text-left" data-text="Çok lezzetli, elinize sağlık.">
                            Çok lezzetli, elinize sağlık.
                        </button>
                        <button class="turkish-test-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded text-sm text-left" data-text="Yarın görüşürüz, iyi geceler.">
                            Yarın görüşürüz, iyi geceler.
                        </button>
                        <button class="turkish-test-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded text-sm text-left" data-text="Türkçe öğreniyorum, çok zor bir dil.">
                            Türkçe öğreniyorum, çok zor bir dil.
                        </button>
                        <button class="turkish-test-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded text-sm text-left" data-text="İstanbul çok büyük ve güzel bir şehir.">
                            İstanbul çok büyük ve güzel bir şehir.
                        </button>
                    </div>
                </div>

                <button id="testTranslate" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 mb-2">Test Translation</button>
                <div id="translateResult" class="text-sm"></div>
            </div>
        </div>
        
        <!-- Browser Compatibility Test -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Browser Compatibility</h2>
            <div id="compatibilityResults" class="space-y-2"></div>
        </div>
        
        <!-- Speech API Test -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Speech API Tests</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-medium text-gray-700 mb-2">Speech Recognition</h3>
                    <button id="testSpeechRecognition" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 mb-2">Test Recognition</button>
                    <div id="speechRecognitionResult" class="text-sm"></div>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-700 mb-2">Speech Synthesis (Text-to-Voice)</h3>
                    <div class="mb-3">
                        <div class="flex gap-2 mb-2">
                            <input type="text" id="testSpeechText" placeholder="Text to speak" class="px-3 py-2 border rounded-lg flex-1">
                            <select id="testSpeechLang" class="px-3 py-2 border rounded-lg">
                                <option value="tr">Turkish</option>
                                <option value="nl">Dutch</option>
                                <option value="en" selected>English</option>
                                <option value="es">Spanish</option>
                                <option value="fr">French</option>
                                <option value="de">German</option>
                            </select>
                        </div>
                        <div class="flex gap-2 mb-2">
                            <button id="testSpeechSynthesis" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700">Speak Text</button>
                            <button id="debugVoicesBtn" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">Debug Voices</button>
                            <button id="listVoicesBtn" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">List All Voices</button>
                        </div>
                    </div>

                    <!-- Turkish Test Sentences for Voice -->
                    <div class="mb-3">
                        <h4 class="text-sm font-medium text-gray-600 mb-2">Quick Turkish Voice Tests:</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                            <button class="turkish-voice-btn bg-orange-100 hover:bg-orange-200 text-orange-800 px-3 py-2 rounded text-sm text-left" data-text="Merhaba, ben Türkçe konuşuyorum." data-lang="tr">
                                🔊 Merhaba, ben Türkçe konuşuyorum.
                            </button>
                            <button class="turkish-voice-btn bg-orange-100 hover:bg-orange-200 text-orange-800 px-3 py-2 rounded text-sm text-left" data-text="Bu bir Türkçe ses testi." data-lang="tr">
                                🔊 Bu bir Türkçe ses testi.
                            </button>
                            <button class="turkish-voice-btn bg-orange-100 hover:bg-orange-200 text-orange-800 px-3 py-2 rounded text-sm text-left" data-text="Türkçe sesli okuma çok önemli." data-lang="tr">
                                🔊 Türkçe sesli okuma çok önemli.
                            </button>
                            <button class="turkish-voice-btn bg-orange-100 hover:bg-orange-200 text-orange-800 px-3 py-2 rounded text-sm text-left" data-text="İstanbul'da hava çok güzel." data-lang="tr">
                                🔊 İstanbul'da hava çok güzel.
                            </button>
                            <button class="turkish-voice-btn bg-green-100 hover:bg-green-200 text-green-800 px-3 py-2 rounded text-sm text-left" data-text="Hallo, ik spreek Nederlands." data-lang="nl">
                                🔊 Hallo, ik spreek Nederlands.
                            </button>
                            <button class="turkish-voice-btn bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded text-sm text-left" data-text="Hello, I am speaking English." data-lang="en">
                                🔊 Hello, I am speaking English.
                            </button>
                        </div>
                    </div>

                    <div id="speechSynthesisResult" class="text-sm"></div>
                    <div id="voiceDebugResult" class="text-xs mt-2 p-2 bg-gray-100 rounded hidden overflow-auto max-h-40"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test suite for Live Translate
        class LiveTranslateTest {
            constructor() {
                this.init();
            }
            
            init() {
                this.setupEventListeners();
                this.runCompatibilityTests();
            }
            
            setupEventListeners() {
                document.getElementById('testAuth').addEventListener('click', () => this.testAuthentication());
                document.getElementById('testTranslate').addEventListener('click', () => this.testTranslation());
                document.getElementById('testSpeechRecognition').addEventListener('click', () => this.testSpeechRecognition());
                document.getElementById('testSpeechSynthesis').addEventListener('click', () => this.testSpeechSynthesis());

                // Turkish test sentence buttons
                document.querySelectorAll('.turkish-test-btn').forEach(button => {
                    button.addEventListener('click', () => {
                        const text = button.getAttribute('data-text');
                        document.getElementById('testText').value = text;
                        // Automatically set to Turkish -> Dutch for testing
                        document.getElementById('testSourceLang').value = 'tr';
                        document.getElementById('testTargetLang').value = 'nl';
                    });
                });

                // Turkish voice test buttons
                document.querySelectorAll('.turkish-voice-btn').forEach(button => {
                    button.addEventListener('click', () => {
                        const text = button.getAttribute('data-text');
                        const lang = button.getAttribute('data-lang');
                        document.getElementById('testSpeechText').value = text;
                        document.getElementById('testSpeechLang').value = lang;
                        // Automatically speak the text
                        this.testSpeechSynthesis();
                    });
                });

                // Debug voices button
                document.getElementById('debugVoicesBtn').addEventListener('click', () => this.debugVoices());

                // List all voices button
                document.getElementById('listVoicesBtn').addEventListener('click', () => this.listAllVoices());
            }
            
            async testAuthentication() {
                const key = document.getElementById('testKey').value;
                const resultDiv = document.getElementById('authResult');
                
                if (!key) {
                    resultDiv.innerHTML = '<span class="text-red-600">Please enter an admin key</span>';
                    return;
                }
                
                try {
                    const response = await fetch('/api/validate-key', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key })
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        resultDiv.innerHTML = '<span class="text-green-600">✓ Authentication successful</span>';
                    } else {
                        resultDiv.innerHTML = `<span class="text-red-600">✗ ${result.error}</span>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<span class="text-red-600">✗ Error: ${error.message}</span>`;
                }
            }
            
            async testTranslation() {
                const text = document.getElementById('testText').value;
                const sourceLang = document.getElementById('testSourceLang').value;
                const targetLang = document.getElementById('testTargetLang').value;
                const resultDiv = document.getElementById('translateResult');
                
                if (!text) {
                    resultDiv.innerHTML = '<span class="text-red-600">Please enter text to translate</span>';
                    return;
                }
                
                resultDiv.innerHTML = '<span class="text-blue-600">Translating...</span>';
                
                try {
                    const response = await fetch('/api/translate', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text, sourceLang, targetLang })
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        resultDiv.innerHTML = `<span class="text-green-600">✓ Translation: "${result.translation}"</span>`;
                    } else {
                        resultDiv.innerHTML = `<span class="text-red-600">✗ ${result.error}</span>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<span class="text-red-600">✗ Error: ${error.message}</span>`;
                }
            }
            
            testSpeechRecognition() {
                const resultDiv = document.getElementById('speechRecognitionResult');
                
                if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                    resultDiv.innerHTML = '<span class="text-green-600">✓ Speech Recognition API supported</span>';
                } else {
                    resultDiv.innerHTML = '<span class="text-red-600">✗ Speech Recognition API not supported</span>';
                }
            }
            
            testSpeechSynthesis() {
                const text = document.getElementById('testSpeechText').value || 'Hello, this is a test';
                const selectedLang = document.getElementById('testSpeechLang').value;
                const resultDiv = document.getElementById('speechSynthesisResult');

                // Language code mapping
                const langCodes = {
                    'tr': 'tr-TR',
                    'nl': 'nl-NL',
                    'en': 'en-US',
                    'es': 'es-ES',
                    'fr': 'fr-FR',
                    'de': 'de-DE'
                };

                if ('speechSynthesis' in window) {
                    try {
                        // Cancel any ongoing speech
                        speechSynthesis.cancel();

                        const utterance = new SpeechSynthesisUtterance(text);
                        utterance.lang = langCodes[selectedLang] || 'en-US';
                        utterance.rate = 0.9;
                        utterance.pitch = 1;

                        // Try to find a voice for the selected language
                        this.ensureVoicesLoaded(() => {
                            const voices = speechSynthesis.getVoices();
                            console.log('Testing speech with language:', selectedLang, 'code:', utterance.lang);

                            // Find appropriate voice using same logic as main app
                            let targetVoice = null;

                            // Strategy 1: Exact match
                            targetVoice = voices.find(voice =>
                                voice.lang.toLowerCase() === utterance.lang.toLowerCase()
                            );

                            // Strategy 2: Language prefix match
                            if (!targetVoice) {
                                const langPrefix = utterance.lang.split('-')[0].toLowerCase();
                                targetVoice = voices.find(voice =>
                                    voice.lang.toLowerCase().startsWith(langPrefix + '-')
                                );
                            }

                            // Strategy 3: Just language code
                            if (!targetVoice) {
                                targetVoice = voices.find(voice =>
                                    voice.lang.toLowerCase().startsWith(selectedLang.toLowerCase())
                                );
                            }

                            // Strategy 4: Special handling for Turkish
                            if (!targetVoice && selectedLang === 'tr') {
                                targetVoice = voices.find(voice =>
                                    voice.lang.toLowerCase().includes('tr') ||
                                    voice.name.toLowerCase().includes('turkish') ||
                                    voice.name.toLowerCase().includes('türk')
                                );
                            }

                            if (targetVoice) {
                                utterance.voice = targetVoice;
                                resultDiv.innerHTML = `<span class="text-green-600">✓ Speaking with voice: ${targetVoice.name} (${targetVoice.lang})</span>`;
                            } else {
                                resultDiv.innerHTML = `<span class="text-yellow-600">⚠ No specific voice found for ${selectedLang}, using default with language: ${utterance.lang}</span>`;
                            }

                            speechSynthesis.speak(utterance);
                        });

                    } catch (error) {
                        resultDiv.innerHTML = `<span class="text-red-600">✗ Error: ${error.message}</span>`;
                    }
                } else {
                    resultDiv.innerHTML = '<span class="text-red-600">✗ Speech Synthesis API not supported</span>';
                }
            }

            ensureVoicesLoaded(callback) {
                const voices = speechSynthesis.getVoices();
                if (voices.length > 0) {
                    callback();
                } else {
                    const voicesChangedHandler = () => {
                        speechSynthesis.removeEventListener('voiceschanged', voicesChangedHandler);
                        callback();
                    };
                    speechSynthesis.addEventListener('voiceschanged', voicesChangedHandler);

                    // Fallback timeout
                    setTimeout(() => {
                        speechSynthesis.removeEventListener('voiceschanged', voicesChangedHandler);
                        callback();
                    }, 1000);
                }
            }
            
            debugVoices() {
                const debugDiv = document.getElementById('voiceDebugResult');
                debugDiv.classList.remove('hidden');

                this.ensureVoicesLoaded(() => {
                    const voices = speechSynthesis.getVoices();

                    let debugInfo = `=== VOICE DEBUG INFO ===\n`;
                    debugInfo += `Total voices: ${voices.length}\n\n`;

                    // Group by language
                    const voicesByLang = {};
                    voices.forEach(voice => {
                        const lang = voice.lang.toLowerCase();
                        if (!voicesByLang[lang]) voicesByLang[lang] = [];
                        voicesByLang[lang].push(voice);
                    });

                    // Turkish voices specifically
                    const turkishVoices = voices.filter(voice =>
                        voice.lang.toLowerCase().includes('tr') ||
                        voice.name.toLowerCase().includes('turkish') ||
                        voice.name.toLowerCase().includes('türk')
                    );

                    debugInfo += `=== TURKISH VOICES FOUND ===\n`;
                    if (turkishVoices.length > 0) {
                        turkishVoices.forEach(voice => {
                            debugInfo += `✓ ${voice.name} (${voice.lang}) - Local: ${voice.localService}, Default: ${voice.default}\n`;
                        });
                    } else {
                        debugInfo += `✗ No Turkish voices found\n`;
                    }

                    debugInfo += `\n=== ALL LANGUAGES ===\n`;
                    Object.keys(voicesByLang).sort().forEach(lang => {
                        debugInfo += `${lang}: ${voicesByLang[lang].length} voices\n`;
                    });

                    debugDiv.innerHTML = `<pre>${debugInfo}</pre>`;
                    console.log(debugInfo);
                });
            }

            listAllVoices() {
                const debugDiv = document.getElementById('voiceDebugResult');
                debugDiv.classList.remove('hidden');

                this.ensureVoicesLoaded(() => {
                    const voices = speechSynthesis.getVoices();

                    let voiceList = `=== ALL AVAILABLE VOICES (${voices.length}) ===\n\n`;

                    voices.forEach((voice, index) => {
                        voiceList += `${index + 1}. ${voice.name}\n`;
                        voiceList += `   Language: ${voice.lang}\n`;
                        voiceList += `   Local: ${voice.localService ? 'Yes' : 'No'}\n`;
                        voiceList += `   Default: ${voice.default ? 'Yes' : 'No'}\n\n`;
                    });

                    debugDiv.innerHTML = `<pre>${voiceList}</pre>`;
                    console.log(voiceList);
                });
            }

            runCompatibilityTests() {
                const tests = [
                    { name: 'localStorage', test: () => typeof Storage !== 'undefined' },
                    { name: 'Fetch API', test: () => typeof fetch !== 'undefined' },
                    { name: 'Web Speech API', test: () => 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window },
                    { name: 'Speech Synthesis', test: () => 'speechSynthesis' in window },
                    { name: 'Microphone Access', test: () => navigator.mediaDevices && navigator.mediaDevices.getUserMedia },
                    { name: 'ES6 Support', test: () => typeof Promise !== 'undefined' && typeof fetch !== 'undefined' }
                ];

                const resultsDiv = document.getElementById('compatibilityResults');

                tests.forEach(test => {
                    const isSupported = test.test();
                    const statusClass = isSupported ? 'text-green-600' : 'text-red-600';
                    const statusIcon = isSupported ? '✓' : '✗';

                    resultsDiv.innerHTML += `<div><span class="${statusClass}">${statusIcon} ${test.name}</span></div>`;
                });
            }
        }
        
        // Initialize test suite
        document.addEventListener('DOMContentLoaded', () => {
            new LiveTranslateTest();
        });
    </script>
</body>
</html>
