<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Translate - Conversational Translation</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="style.css">
    <meta name="description" content="Real-time voice translation for conversations between two people">
    <meta name="theme-color" content="#3B82F6">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Authentication Modal -->
    <div id="authModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">Access Required</h2>
            <p class="text-gray-600 mb-6">Please enter the admin key to access Live Translate</p>
            <input 
                type="password" 
                id="adminKeyInput" 
                placeholder="Enter admin key"
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-4"
            >
            <button 
                id="submitKey" 
                class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
            >
                Access Application
            </button>
            <div id="authError" class="text-red-500 text-sm mt-2 hidden"></div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="hidden">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="max-w-6xl mx-auto px-4 py-4">
                <div class="flex justify-between items-center">
                    <h1 class="text-2xl font-bold text-gray-800">Live Translate</h1>
                    <button id="logoutBtn" class="text-gray-600 hover:text-gray-800 text-sm">
                        Logout
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-6xl mx-auto px-4 py-6">
            <!-- Language Selection -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Language Settings</h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Person A Language</label>
                        <select id="langA" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="tr" selected>Turkish</option>
                            <option value="nl">Dutch</option>
                            <option value="en">English</option>
                            <option value="es">Spanish</option>
                            <option value="fr">French</option>
                            <option value="de">German</option>
                            <option value="it">Italian</option>
                            <option value="pt">Portuguese</option>
                            <option value="ru">Russian</option>
                            <option value="ja">Japanese</option>
                            <option value="ko">Korean</option>
                            <option value="zh">Chinese (Mandarin)</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Person B Language</label>
                        <select id="langB" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="nl" selected>Dutch</option>
                            <option value="tr">Turkish</option>
                            <option value="en">English</option>
                            <option value="es">Spanish</option>
                            <option value="fr">French</option>
                            <option value="de">German</option>
                            <option value="it">Italian</option>
                            <option value="pt">Portuguese</option>
                            <option value="ru">Russian</option>
                            <option value="ja">Japanese</option>
                            <option value="ko">Korean</option>
                            <option value="zh">Chinese (Mandarin)</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Conversation Interface -->
            <div class="grid md:grid-cols-2 gap-6">
                <!-- Person A -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-blue-600">Person A</h3>
                        <span id="langADisplay" class="text-sm text-gray-500">Turkish</span>
                    </div>
                    
                    <!-- Microphone Button -->
                    <div class="text-center mb-6">
                        <button 
                            id="micA" 
                            class="w-20 h-20 bg-blue-600 hover:bg-blue-700 text-white rounded-full flex items-center justify-center transition-all duration-200 mx-auto"
                        >
                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                        <p class="text-sm text-gray-600 mt-2">Hold to speak</p>
                    </div>

                    <!-- Speech Display -->
                    <div class="mb-4">
                        <div class="bg-gray-50 rounded-lg p-4 min-h-[100px]">
                            <div id="speechA" class="text-gray-800"></div>
                        </div>
                    </div>

                    <!-- Translation Display -->
                    <div class="mb-4">
                        <div class="bg-blue-50 rounded-lg p-4 min-h-[100px]">
                            <div id="translationA" class="text-blue-800"></div>
                        </div>
                    </div>

                    <!-- Playback Controls -->
                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" id="autoPlayA" class="mr-2">
                            <span class="text-sm text-gray-700">Auto-play translation</span>
                        </label>
                        <button 
                            id="playA" 
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm disabled:opacity-50"
                            disabled
                        >
                            Play
                        </button>
                    </div>
                </div>

                <!-- Person B -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-green-600">Person B</h3>
                        <span id="langBDisplay" class="text-sm text-gray-500">Dutch</span>
                    </div>
                    
                    <!-- Microphone Button -->
                    <div class="text-center mb-6">
                        <button 
                            id="micB" 
                            class="w-20 h-20 bg-green-600 hover:bg-green-700 text-white rounded-full flex items-center justify-center transition-all duration-200 mx-auto"
                        >
                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                        <p class="text-sm text-gray-600 mt-2">Hold to speak</p>
                    </div>

                    <!-- Speech Display -->
                    <div class="mb-4">
                        <div class="bg-gray-50 rounded-lg p-4 min-h-[100px]">
                            <div id="speechB" class="text-gray-800"></div>
                        </div>
                    </div>

                    <!-- Translation Display -->
                    <div class="mb-4">
                        <div class="bg-green-50 rounded-lg p-4 min-h-[100px]">
                            <div id="translationB" class="text-green-800"></div>
                        </div>
                    </div>

                    <!-- Playback Controls -->
                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" id="autoPlayB" class="mr-2">
                            <span class="text-sm text-gray-700">Auto-play translation</span>
                        </label>
                        <button 
                            id="playB" 
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm disabled:opacity-50"
                            disabled
                        >
                            Play
                        </button>
                    </div>
                </div>
            </div>

            <!-- Status Indicator -->
            <div class="mt-6 text-center">
                <div id="statusIndicator" class="inline-flex items-center px-4 py-2 rounded-full bg-gray-100 text-gray-600">
                    <div class="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                    Ready to translate
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
