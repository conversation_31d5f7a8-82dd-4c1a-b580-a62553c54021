import { Client } from "@gradio/client";

export default async function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    if (req.method !== 'POST') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    try {
        const { text, language } = req.body;

        if (!text || !language) {
            return res.status(400).json({ error: 'Text and language are required' });
        }

        console.log('TTS request:', { text, language });

        // Language to Hugging Face model mapping
        const hfModels = {
            'tr': {
                language: 'Turkish',
                model: 'csukuangfj/vits-piper-tr_TR-fettah-medium|1 speaker'
            },
            'nl': {
                language: 'Dutch',
                model: 'csukuangfj/vits-piper-nl_BE-nathalie-medium'
            },
            'en': {
                language: 'English',
                model: 'csukuangfj/vits-ljs|1 speaker'
            },
            'es': {
                language: 'Spanish',
                model: 'csukuangfj/vits-piper-es_ES-davefx-medium'
            },
            'fr': {
                language: 'French',
                model: 'csukuangfj/vits-piper-fr_FR-siwis-medium'
            },
            'de': {
                language: 'German',
                model: 'csukuangfj/vits-piper-de_DE-thorsten-medium|1 speaker'
            }
        };

        const modelInfo = hfModels[language];
        if (!modelInfo) {
            return res.status(400).json({ error: `Language ${language} not supported` });
        }

        console.log('Using model:', modelInfo);

        // Connect to Hugging Face Gradio client
        const client = await Client.connect("k2-fsa/text-to-speech");
        
        console.log('Connected to Hugging Face client');

        // Call the process endpoint
        const result = await client.predict("/process", {
            language: modelInfo.language,
            repo_id: modelInfo.model,
            text: text,
            sid: "0",
            speed: 1.0
        });

        console.log('TTS result:', result);

        // The result should contain the audio file URL
        if (result.data && result.data[0] && result.data[0].url) {
            const audioUrl = result.data[0].url;
            console.log('Audio URL:', audioUrl);
            
            return res.status(200).json({ 
                success: true, 
                audioUrl: audioUrl,
                info: result.data[1] || 'TTS completed successfully'
            });
        } else {
            console.error('Unexpected result format:', result);
            return res.status(500).json({ error: 'Unexpected response format from TTS service' });
        }

    } catch (error) {
        console.error('TTS error:', error);
        return res.status(500).json({ 
            error: 'TTS generation failed', 
            details: error.message 
        });
    }
}
