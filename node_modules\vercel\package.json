{"name": "vercel", "version": "32.7.2", "preferGlobal": true, "license": "Apache-2.0", "description": "The command-line interface for Vercel", "homepage": "https://vercel.com", "repository": {"type": "git", "url": "https://github.com/vercel/vercel.git", "directory": "packages/cli"}, "bin": {"vc": "./dist/index.js", "vercel": "./dist/index.js"}, "files": ["dist"], "engines": {"node": ">= 16"}, "dependencies": {"@vercel/build-utils": "7.3.0", "@vercel/fun": "1.1.0", "@vercel/go": "3.0.4", "@vercel/hydrogen": "1.0.1", "@vercel/next": "4.0.15", "@vercel/node": "3.0.12", "@vercel/python": "4.1.0", "@vercel/redwood": "2.0.5", "@vercel/remix-builder": "2.0.14", "@vercel/ruby": "2.0.4", "@vercel/static-build": "2.0.14", "chokidar": "3.3.1"}, "devDependencies": {"@alex_neo/jest-expect-message": "1.0.5", "@edge-runtime/node-utils": "2.2.2", "@next/env": "11.1.2", "@sentry/node": "5.5.0", "@sindresorhus/slugify": "0.11.0", "@swc/core": "1.2.218", "@tootallnate/once": "1.1.2", "@types/async-retry": "1.2.1", "@types/bytes": "3.0.0", "@types/chance": "1.1.3", "@types/debug": "0.0.31", "@types/dotenv": "6.1.1", "@types/escape-html": "0.0.20", "@types/express": "4.17.13", "@types/fs-extra": "9.0.13", "@types/glob": "7.1.1", "@types/http-proxy": "1.16.2", "@types/ini": "1.3.31", "@types/inquirer": "7.3.1", "@types/jest": "27.4.1", "@types/jest-expect-message": "1.0.3", "@types/json-parse-better-errors": "1.0.0", "@types/load-json-file": "2.0.7", "@types/mime-types": "2.1.0", "@types/minimatch": "3.0.3", "@types/ms": "0.7.30", "@types/node": "14.18.33", "@types/node-fetch": "2.5.10", "@types/npm-package-arg": "6.1.0", "@types/pluralize": "0.0.29", "@types/psl": "1.1.0", "@types/qs": "6.9.7", "@types/semver": "6.0.1", "@types/tar-fs": "1.16.1", "@types/text-table": "0.2.0", "@types/title": "3.4.1", "@types/universal-analytics": "0.4.2", "@types/update-notifier": "5.1.0", "@types/which": "3.0.0", "@types/write-json-file": "2.2.1", "@types/yauzl-promise": "2.1.0", "@vercel-internals/constants": "1.0.4", "@vercel-internals/get-package-json": "1.0.0", "@vercel-internals/types": "1.0.17", "@vercel/client": "13.0.10", "@vercel/error-utils": "2.0.2", "@vercel/frameworks": "2.0.5", "@vercel/fs-detectors": "5.1.5", "@vercel/routing-utils": "3.1.0", "ajv": "6.12.2", "alpha-sort": "2.0.1", "ansi-escapes": "4.3.2", "ansi-regex": "5.0.1", "arg": "5.0.0", "async-listen": "3.0.0", "async-retry": "1.1.3", "async-sema": "2.1.4", "bytes": "3.0.0", "chalk": "4.1.0", "chance": "1.1.7", "cli-table3": "0.6.3", "codecov": "3.8.2", "date-fns": "1.29.0", "debug": "3.1.0", "dot": "1.1.3", "dotenv": "4.0.0", "email-validator": "1.1.1", "epipebomb": "1.0.0", "escape-html": "1.0.3", "esm": "3.1.4", "execa": "3.2.0", "expect": "29.5.0", "express": "4.17.1", "fast-deep-equal": "3.1.3", "find-up": "4.1.0", "fs-extra": "10.0.0", "get-port": "5.1.1", "git-last-commit": "1.0.1", "glob": "7.1.2", "http-proxy": "1.18.1", "ini": "3.0.0", "inquirer": "7.0.4", "is-docker": "2.2.1", "is-port-reachable": "3.1.0", "is-url": "1.2.2", "jaro-winkler": "0.2.8", "jest-junit": "16.0.0", "jest-matcher-utils": "29.3.1", "json-parse-better-errors": "1.0.2", "jsonlines": "0.1.1", "line-async-iterator": "3.0.0", "load-json-file": "3.0.0", "mime-types": "2.1.24", "minimatch": "3.1.2", "ms": "2.1.2", "node-fetch": "2.6.7", "npm-package-arg": "6.1.0", "open": "8.4.0", "ora": "3.4.0", "pcre-to-regexp": "1.0.0", "pluralize": "7.0.0", "promisepipe": "3.0.0", "proxy": "2.0.0", "proxy-agent": "6.3.0", "psl": "1.1.31", "qr-image": "3.2.0", "raw-body": "2.4.1", "rimraf": "3.0.2", "semver": "5.7.2", "serve-handler": "6.1.1", "strip-ansi": "6.0.1", "supports-hyperlinks": "3.0.0", "tar-fs": "1.16.3", "text-table": "0.2.0", "title": "3.4.1", "tmp-promise": "1.0.3", "tree-kill": "1.2.2", "ts-node": "10.9.1", "universal-analytics": "0.4.20", "utility-types": "2.1.0", "which": "3.0.0", "write-json-file": "2.2.0", "xdg-app-paths": "5.1.0", "yauzl-promise": "2.1.3"}, "scripts": {"test": "jest --reporters=default --reporters=jest-junit --env node --verbose --bail", "test-unit": "pnpm test test/unit/", "test-e2e": "rimraf test/fixtures/integration && pnpm test test/integration-1.test.ts test/integration-2.test.ts test/integration-3.test.ts", "test-dev": "pnpm test test/dev/", "coverage": "codecov", "build": "node scripts/build.mjs", "dev": "ts-node ./src/index.ts", "type-check": "tsc --noEmit"}}