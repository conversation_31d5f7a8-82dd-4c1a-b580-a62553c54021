// Serverless function for admin key validation
export default async function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }
    
    if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
    }
    
    try {
        const { key } = req.body;
        
        if (!key) {
            res.status(400).json({ error: 'Admin key is required' });
            return;
        }
        
        // Validate against environment variable
        const adminKey = process.env.ADMIN_KEY;
        
        if (!adminKey) {
            console.error('ADMIN_KEY environment variable not set');
            res.status(500).json({ error: 'Server configuration error' });
            return;
        }
        
        if (key === adminKey) {
            res.status(200).json({ 
                success: true,
                message: 'Authentication successful'
            });
        } else {
            res.status(401).json({ 
                error: 'Invalid admin key'
            });
        }
        
    } catch (error) {
        console.error('Validation error:', error);
        res.status(500).json({ 
            error: 'Validation failed',
            details: error.message 
        });
    }
}
