// Live Translate - Main Application Logic
class LiveTranslate {
    constructor() {
        this.isAuthenticated = false;
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        this.currentSpeaker = null;
        this.conversationHistory = [];
        this.accumulatedTranscript = '';
        this.maxRecordingTimer = null;
        this.maxRecordingTime = 45000; // 45 seconds max recording time
        this.isTranslating = false; // Flag to prevent duplicate translations
        
        // Language mappings
        this.languages = {
            'tr': { name: 'Turkish', code: 'tr-TR' },
            'nl': { name: 'Dutch', code: 'nl-NL' },
            'en': { name: 'English', code: 'en-US' },
            'es': { name: 'Spanish', code: 'es-ES' },
            'fr': { name: 'French', code: 'fr-FR' },
            'de': { name: 'German', code: 'de-DE' },
            'it': { name: 'Italian', code: 'it-IT' },
            'pt': { name: 'Portuguese', code: 'pt-PT' },
            'ru': { name: 'Russian', code: 'ru-RU' },
            'ja': { name: 'Japanese', code: 'ja-<PERSON>' },
            'ko': { name: 'Korean', code: 'ko-K<PERSON>' },
            'zh': { name: 'Chinese (Mandarin)', code: 'zh-CN' }
        };
        
        this.init();
    }
    
    init() {
        this.checkAuthentication();
        this.setupEventListeners();
        this.initializeSpeechRecognition();
        this.loadSettings();
    }
    
    checkAuthentication() {
        const cachedAuth = localStorage.getItem('liveTranslateAuth');
        if (cachedAuth) {
            const authData = JSON.parse(cachedAuth);
            const now = new Date().getTime();
            
            // Check if cached auth is still valid (1 hour)
            if (now - authData.timestamp < 3600000) {
                this.isAuthenticated = true;
                this.showMainApp();
                return;
            } else {
                localStorage.removeItem('liveTranslateAuth');
            }
        }
        
        this.showAuthModal();
    }
    
    showAuthModal() {
        document.getElementById('authModal').classList.remove('hidden');
        document.getElementById('mainApp').classList.add('hidden');
        document.getElementById('adminKeyInput').focus();
    }
    
    showMainApp() {
        document.getElementById('authModal').classList.add('hidden');
        document.getElementById('mainApp').classList.remove('hidden');
        this.updateLanguageDisplays();
    }
    
    async validateAdminKey(key) {
        try {
            const response = await fetch('/api/validate-key', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ key })
            });

            if (response.ok) {
                const result = await response.json();
                const authData = {
                    timestamp: new Date().getTime(),
                    key: key
                };
                localStorage.setItem('liveTranslateAuth', JSON.stringify(authData));
                this.isAuthenticated = true;
                this.showMainApp();
                return true;
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Invalid admin key');
            }
        } catch (error) {
            console.error('Authentication error:', error);
            this.showAuthError(error.message || 'Authentication failed. Please try again.');
            return false;
        }
    }
    
    showAuthError(message) {
        const errorDiv = document.getElementById('authError');
        errorDiv.textContent = message;
        errorDiv.classList.remove('hidden');
        setTimeout(() => {
            errorDiv.classList.add('hidden');
        }, 5000);
    }
    
    setupEventListeners() {
        // Authentication
        document.getElementById('submitKey').addEventListener('click', () => {
            const key = document.getElementById('adminKeyInput').value;
            if (key) {
                this.validateAdminKey(key);
            }
        });
        
        document.getElementById('adminKeyInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('submitKey').click();
            }
        });
        
        // Logout
        document.getElementById('logoutBtn').addEventListener('click', () => {
            localStorage.removeItem('liveTranslateAuth');
            this.isAuthenticated = false;
            this.showAuthModal();
        });
        
        // Language selectors
        document.getElementById('langA').addEventListener('change', () => {
            this.updateLanguageDisplays();
            this.saveSettings();
        });
        
        document.getElementById('langB').addEventListener('change', () => {
            this.updateLanguageDisplays();
            this.saveSettings();
        });
        
        // Microphone buttons
        this.setupMicrophoneEvents('A');
        this.setupMicrophoneEvents('B');
        
        // Play buttons
        document.getElementById('playA').addEventListener('click', () => {
            this.playTranslation('A');
        });
        
        document.getElementById('playB').addEventListener('click', () => {
            this.playTranslation('B');
        });
        
        // Auto-play checkboxes
        document.getElementById('autoPlayA').addEventListener('change', this.saveSettings.bind(this));
        document.getElementById('autoPlayB').addEventListener('change', this.saveSettings.bind(this));
    }
    
    setupMicrophoneEvents(person) {
        const micButton = document.getElementById(`mic${person}`);
        
        // Mouse events
        micButton.addEventListener('mousedown', () => this.startRecording(person));
        micButton.addEventListener('mouseup', () => this.stopRecording(person));
        micButton.addEventListener('mouseleave', () => this.stopRecording(person));
        
        // Touch events for mobile
        micButton.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.startRecording(person);
        });
        
        micButton.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.stopRecording(person);
        });
        
        // Keyboard events for accessibility
        micButton.addEventListener('keydown', (e) => {
            if (e.key === ' ' || e.key === 'Enter') {
                e.preventDefault();
                this.startRecording(person);
            }
        });
        
        micButton.addEventListener('keyup', (e) => {
            if (e.key === ' ' || e.key === 'Enter') {
                e.preventDefault();
                this.stopRecording(person);
            }
        });
    }
    
    initializeSpeechRecognition() {
        if ('webkitSpeechRecognition' in window) {
            this.recognition = new webkitSpeechRecognition();
        } else if ('SpeechRecognition' in window) {
            this.recognition = new SpeechRecognition();
        } else {
            console.error('Speech recognition not supported');
            this.updateStatus('Speech recognition not supported in this browser', 'error');
            return;
        }

        // Configure for longer speech input
        this.recognition.continuous = true;
        this.recognition.interimResults = true;
        this.recognition.maxAlternatives = 1;
        
        this.recognition.onresult = (event) => {
            let finalTranscript = '';
            let interimTranscript = '';

            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                if (event.results[i].isFinal) {
                    finalTranscript += transcript;
                } else {
                    interimTranscript += transcript;
                }
            }

            if (this.currentSpeaker) {
                const speechDiv = document.getElementById(`speech${this.currentSpeaker}`);

                // Accumulate final transcript
                if (finalTranscript) {
                    this.accumulatedTranscript += finalTranscript;
                }

                // Display current accumulated + interim text
                const displayText = this.accumulatedTranscript + (interimTranscript ? ' ' + interimTranscript : '');
                speechDiv.textContent = displayText;
            }
        };
        
        this.recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            this.updateStatus(`Speech recognition error: ${event.error}`, 'error');
            this.resetRecordingState();
        };
        
        this.recognition.onend = () => {
            console.log('Recognition ended. Current speaker:', this.currentSpeaker);
            console.log('Accumulated transcript:', this.accumulatedTranscript);

            // Only translate if we haven't already started translation from stopRecording
            // The onend event can fire after stopRecording timeout has already handled translation
            if (this.currentSpeaker && this.accumulatedTranscript && this.accumulatedTranscript.trim()) {
                console.log('Calling translateText from onend with:', this.accumulatedTranscript.trim());
                const transcript = this.accumulatedTranscript.trim();
                const speaker = this.currentSpeaker;
                this.translateText(transcript, speaker);
            } else {
                console.log('Not translating from onend - missing speaker or transcript');
                // Only reset if we haven't already reset
                if (this.currentSpeaker) {
                    this.resetRecordingState();
                }
            }
        };
    }
    
    startRecording(person) {
        if (this.currentSpeaker || !this.recognition) return;

        this.currentSpeaker = person;
        this.accumulatedTranscript = ''; // Reset accumulated transcript
        const sourceLang = document.getElementById(`lang${person}`).value;

        // Set recognition language
        this.recognition.lang = this.languages[sourceLang].code;

        // Update UI
        const micButton = document.getElementById(`mic${person}`);
        micButton.classList.add('mic-recording');

        // Clear previous content
        document.getElementById(`speech${person}`).textContent = '';
        document.getElementById(`translation${person}`).textContent = '';
        document.getElementById(`play${person}`).disabled = true;

        this.updateStatus('Listening...', 'listening');

        // Set maximum recording time limit (45 seconds)
        this.maxRecordingTimer = setTimeout(() => {
            if (this.currentSpeaker === person) {
                this.updateStatus('Maximum recording time reached', 'ready');
                this.stopRecording(person);
            }
        }, this.maxRecordingTime);

        try {
            this.recognition.start();
        } catch (error) {
            console.error('Failed to start recognition:', error);
            this.resetRecordingState();
        }
    }
    
    stopRecording(person) {
        if (this.currentSpeaker !== person || !this.recognition) return;

        // Clear timers
        if (this.maxRecordingTimer) {
            clearTimeout(this.maxRecordingTimer);
            this.maxRecordingTimer = null;
        }

        // Store current state before stopping recognition
        const currentTranscript = this.accumulatedTranscript;
        const currentSpeaker = this.currentSpeaker;

        try {
            this.recognition.stop();
        } catch (error) {
            console.error('Failed to stop recognition:', error);
        }

        // Update UI immediately but don't reset state yet
        const micButton = document.getElementById(`mic${person}`);
        micButton.classList.remove('mic-recording');
        this.updateStatus('Processing...', 'translating');

        // Process any accumulated transcript when user stops recording
        setTimeout(() => {
            console.log('stopRecording timeout - checking transcript:', currentTranscript);
            if (currentTranscript && currentTranscript.trim()) {
                console.log('Calling translateText from stopRecording timeout');
                this.translateText(currentTranscript.trim(), currentSpeaker);
            } else {
                // If no transcript, reset state
                this.resetRecordingState();
            }
        }, 500); // Small delay to ensure recognition has finished
    }
    
    resetRecordingState() {
        if (this.currentSpeaker) {
            const micButton = document.getElementById(`mic${this.currentSpeaker}`);
            micButton.classList.remove('mic-recording');
        }

        // Clear all timers
        if (this.maxRecordingTimer) {
            clearTimeout(this.maxRecordingTimer);
            this.maxRecordingTimer = null;
        }

        this.currentSpeaker = null;
        this.accumulatedTranscript = '';
        this.isTranslating = false;
        this.updateStatus('Ready to translate', 'ready');
    }
    
    async translateText(text, speaker) {
        console.log('translateText called with:', { text, speaker });

        // Prevent duplicate translations
        if (this.isTranslating) {
            console.log('Translation already in progress, skipping');
            return;
        }

        this.isTranslating = true;

        const sourceLang = document.getElementById(`lang${speaker}`).value;
        const targetLang = speaker === 'A' ? document.getElementById('langB').value : document.getElementById('langA').value;

        console.log('Translation params:', { sourceLang, targetLang });

        this.updateStatus('Translating...', 'translating');

        try {
            console.log('Making API request to /api/translate');
            const response = await fetch('/api/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    text,
                    sourceLang,
                    targetLang,
                    context: this.conversationHistory.slice(-5) // Last 5 exchanges for context
                })
            });

            console.log('API response status:', response.status);

            if (!response.ok) {
                const errorData = await response.json();
                console.error('API error response:', errorData);
                throw new Error(`Translation failed: ${errorData.error || response.status}`);
            }

            const data = await response.json();
            console.log('Translation response:', data);
            const translatedText = data.translation;
            
            // Display translation
            document.getElementById(`translation${speaker}`).textContent = translatedText;
            document.getElementById(`play${speaker}`).disabled = false;
            
            // Add to conversation history
            this.conversationHistory.push({
                speaker,
                original: text,
                translation: translatedText,
                sourceLang,
                targetLang,
                timestamp: new Date().toISOString()
            });
            
            // Auto-play if enabled
            const autoPlay = document.getElementById(`autoPlay${speaker}`).checked;
            if (autoPlay) {
                this.playTranslation(speaker);
            }
            
            this.updateStatus('Translation complete', 'ready');

        } catch (error) {
            console.error('Translation error:', error);
            this.updateStatus('Translation failed. Please try again.', 'error');
            document.getElementById(`translation${speaker}`).textContent = 'Translation failed. Please try again.';
        } finally {
            // Always reset recording state and translation flag after translation attempt
            this.isTranslating = false;
            this.resetRecordingState();
        }
    }
    
    playTranslation(speaker) {
        const translationText = document.getElementById(`translation${speaker}`).textContent;
        if (!translationText || translationText === 'Translation failed. Please try again.') return;

        const targetLang = speaker === 'A' ? document.getElementById('langB').value : document.getElementById('langA').value;

        // Cancel any ongoing speech
        this.synthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(translationText);
        utterance.lang = this.languages[targetLang].code;
        utterance.rate = 0.9;
        utterance.pitch = 1;

        // Wait for voices to be loaded if they aren't already
        this.ensureVoicesLoaded(() => {
            const voices = this.synthesis.getVoices();
            console.log('Available voices:', voices.map(v => ({ name: v.name, lang: v.lang })));
            console.log('Looking for voice for target language:', targetLang, 'with code:', this.languages[targetLang].code);

            // Try multiple strategies to find the best voice
            let targetVoice = null;

            // Strategy 1: Exact match with full language code (e.g., 'tr-TR')
            targetVoice = voices.find(voice =>
                voice.lang.toLowerCase() === this.languages[targetLang].code.toLowerCase()
            );

            // Strategy 2: Match language part only (e.g., 'tr' matches 'tr-TR', 'tr-CY', etc.)
            if (!targetVoice) {
                const langPrefix = this.languages[targetLang].code.split('-')[0].toLowerCase();
                targetVoice = voices.find(voice =>
                    voice.lang.toLowerCase().startsWith(langPrefix + '-')
                );
            }

            // Strategy 3: Match just the language code without region
            if (!targetVoice) {
                targetVoice = voices.find(voice =>
                    voice.lang.toLowerCase().startsWith(targetLang.toLowerCase())
                );
            }

            // Strategy 4: For Turkish specifically, look for any voice containing 'tr'
            if (!targetVoice && targetLang === 'tr') {
                targetVoice = voices.find(voice =>
                    voice.lang.toLowerCase().includes('tr') ||
                    voice.name.toLowerCase().includes('turkish') ||
                    voice.name.toLowerCase().includes('türk')
                );
            }

            if (targetVoice) {
                utterance.voice = targetVoice;
                console.log('Selected voice:', targetVoice.name, 'with language:', targetVoice.lang);
                this.synthesis.speak(utterance);
            } else {
                console.warn('No suitable voice found for language:', targetLang);
                console.log('Attempting fallback to Hugging Face TTS...');

                // Fallback to Hugging Face TTS for Turkish and other unsupported languages
                this.fallbackToHuggingFaceTTS(translationText, targetLang);
            }
        });
    }

    ensureVoicesLoaded(callback) {
        const voices = this.synthesis.getVoices();
        if (voices.length > 0) {
            callback();
        } else {
            // Voices not loaded yet, wait for them
            const voicesChangedHandler = () => {
                this.synthesis.removeEventListener('voiceschanged', voicesChangedHandler);
                callback();
            };
            this.synthesis.addEventListener('voiceschanged', voicesChangedHandler);

            // Fallback timeout in case voiceschanged never fires
            setTimeout(() => {
                this.synthesis.removeEventListener('voiceschanged', voicesChangedHandler);
                callback();
            }, 1000);
        }
    }

    async fallbackToHuggingFaceTTS(text, targetLang) {
        try {
            this.updateStatus('Loading cloud voice...', 'translating');

            console.log('Calling server-side TTS API...');

            // Call our server-side TTS endpoint
            const response = await fetch('/api/tts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    text: text,
                    language: targetLang
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('TTS API response:', result);

            if (result.success && result.audioUrl) {
                console.log('Playing audio from URL:', result.audioUrl);
                this.playAudioFromUrl(result.audioUrl);
                this.updateStatus('Translation complete', 'ready');
            } else {
                throw new Error('Invalid response from TTS service');
            }

        } catch (error) {
            console.error('TTS error:', error);
            this.updateStatus('Cloud voice failed, please try again', 'error');
        }
    }



    playAudioFromUrl(audioUrl) {
        try {
            const audio = new Audio(audioUrl);
            audio.play().catch(error => {
                console.error('Error playing audio:', error);
                this.updateStatus('Error playing audio', 'error');
            });
        } catch (error) {
            console.error('Error creating audio element:', error);
            this.updateStatus('Error playing audio', 'error');
        }
    }
    
    updateLanguageDisplays() {
        const langA = document.getElementById('langA').value;
        const langB = document.getElementById('langB').value;
        
        document.getElementById('langADisplay').textContent = this.languages[langA].name;
        document.getElementById('langBDisplay').textContent = this.languages[langB].name;
    }
    
    updateStatus(message, type = 'ready') {
        const statusIndicator = document.getElementById('statusIndicator');
        const statusDot = statusIndicator.querySelector('div');
        
        statusIndicator.className = `inline-flex items-center px-4 py-2 rounded-full status-${type}`;
        statusDot.className = `w-2 h-2 rounded-full mr-2 status-dot`;
        statusIndicator.querySelector('div:last-child').textContent = message;
    }
    
    saveSettings() {
        const settings = {
            langA: document.getElementById('langA').value,
            langB: document.getElementById('langB').value,
            autoPlayA: document.getElementById('autoPlayA').checked,
            autoPlayB: document.getElementById('autoPlayB').checked
        };
        
        localStorage.setItem('liveTranslateSettings', JSON.stringify(settings));
    }
    
    loadSettings() {
        const settings = localStorage.getItem('liveTranslateSettings');
        if (settings) {
            const parsed = JSON.parse(settings);

            document.getElementById('langA').value = parsed.langA || 'tr';
            document.getElementById('langB').value = parsed.langB || 'nl';
            document.getElementById('autoPlayA').checked = parsed.autoPlayA || false;
            document.getElementById('autoPlayB').checked = parsed.autoPlayB || false;

            this.updateLanguageDisplays();
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LiveTranslate();
});

// Handle page visibility changes to manage speech synthesis
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // Cancel speech synthesis when page is hidden
        if (window.speechSynthesis) {
            window.speechSynthesis.cancel();
        }
    }
});
