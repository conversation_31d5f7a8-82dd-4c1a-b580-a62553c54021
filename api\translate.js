// Serverless function for OpenAI translation
export default async function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }
    
    if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
    }
    
    try {
        const { text, sourceLang, targetLang, context = [] } = req.body;
        
        if (!text || !sourceLang || !targetLang) {
            res.status(400).json({ error: 'Missing required parameters' });
            return;
        }
        
        // Language name mappings for better OpenAI understanding
        const languageNames = {
            'tr': 'Turkish',
            'nl': 'Dutch',
            'en': 'English',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ru': 'Russian',
            'ja': 'Japanese',
            'ko': 'Korean',
            'zh': 'Chinese (Mandarin)'
        };
        
        const sourceLanguage = languageNames[sourceLang] || sourceLang;
        const targetLanguage = languageNames[targetLang] || targetLang;
        
        // Build context from conversation history
        let contextPrompt = '';
        if (context.length > 0) {
            contextPrompt = '\n\nConversation context (for reference):\n';
            context.forEach((entry, index) => {
                contextPrompt += `${index + 1}. ${entry.original} → ${entry.translation}\n`;
            });
        }
        
        // Create system prompt for conversational translation
        const systemPrompt = `You are a professional translator specializing in real-time conversational translation. Your task is to translate spoken language in a natural, conversational manner that preserves the speaker's intent and tone.

Guidelines:
- Translate from ${sourceLanguage} to ${targetLanguage}
- Maintain conversational tone and natural flow
- Preserve emotional context and intent
- Use appropriate formality level for the context
- Handle colloquialisms and informal speech naturally
- Keep translations concise but complete
- If the text is unclear or incomplete, provide the best possible translation${contextPrompt}

Translate the following text:`;
        
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model: 'gpt-4',
                messages: [
                    {
                        role: 'system',
                        content: systemPrompt
                    },
                    {
                        role: 'user',
                        content: text
                    }
                ],
                max_tokens: 500,
                temperature: 0.3,
                top_p: 1,
                frequency_penalty: 0,
                presence_penalty: 0
            })
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            console.error('OpenAI API error:', errorData);
            throw new Error(`OpenAI API error: ${response.status}`);
        }
        
        const data = await response.json();
        const translation = data.choices[0]?.message?.content?.trim();
        
        if (!translation) {
            throw new Error('No translation received from OpenAI');
        }
        
        res.status(200).json({
            translation,
            sourceLang,
            targetLang,
            originalText: text
        });
        
    } catch (error) {
        console.error('Translation error:', error);
        res.status(500).json({ 
            error: 'Translation failed',
            details: error.message 
        });
    }
}
