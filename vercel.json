{"functions": {"api/translate.js": {"runtime": "@vercel/node"}, "api/validate-key.js": {"runtime": "@vercel/node"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "microphone=*, camera=(), geolocation=(), payment=()"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}]}