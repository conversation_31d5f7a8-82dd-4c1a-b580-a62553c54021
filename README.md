# Live Translate - Conversational Translation App

A real-time voice-to-voice translation web application designed for conversations between two people speaking different languages.

## Features

- **Voice-to-Voice Translation**: Push-to-talk interface with extended speech recognition (up to 45 seconds) and synthesis
- **Conversational Design**: Optimized for two-person conversations with context awareness
- **Manual Playback Control**: Toggle switches for auto-play with manual override options
- **Mobile Optimized**: Responsive design for real-world conversation scenarios
- **Secure Authentication**: Admin key protection with 1-hour session caching
- **Multiple Languages**: Support for 12+ languages including Turkish, Dutch, English, Spanish, French, German, Italian, Portuguese, Russian, Japanese, Korean, and Chinese (defaults to Turkish → Dutch)
- **Accessibility**: Screen reader support, keyboard navigation, and high contrast mode
- **Performance**: Lightweight vanilla JavaScript targeting Lighthouse scores ≥95

## Setup Instructions

### 1. Environment Configuration

1. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your credentials:
   ```env
   OPENAI_API_KEY=your_openai_api_key_here
   ADMIN_KEY=your_secure_admin_key_here
   ```

### 2. Get OpenAI API Key

1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create a new API key
3. Add it to your `.env` file

### 3. Local Development

#### Option A: Using Vercel CLI (Recommended)

1. Install Vercel CLI:
   ```bash
   npm install -g vercel
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start development server:
   ```bash
   npm run dev
   ```

4. Open http://localhost:3000

#### Option B: Static File Server

For basic testing without serverless functions:

1. Use any static file server (Python, Node.js, etc.)
2. Note: API functions won't work without serverless environment

### 4. Deployment

#### Deploy to Vercel

1. Install Vercel CLI if not already installed
2. Login to Vercel:
   ```bash
   vercel login
   ```

3. Deploy:
   ```bash
   npm run deploy
   ```

4. Set environment variables in Vercel dashboard:
   - Go to your project settings
   - Add `OPENAI_API_KEY` and `ADMIN_KEY` in Environment Variables

## Usage

### Authentication
1. Enter your admin key when prompted
2. Key is cached for 1 hour to avoid re-entry

### Translation Process
1. **Select Languages**: Choose source and target languages for both people
2. **Push to Talk**: Hold microphone button to speak
3. **View Translation**: See original text and translation
4. **Manual Playback**: Use toggle switches to control audio playback
5. **Continue Conversation**: Switch between speakers naturally

### Settings
- **Language Selection**: Dropdown menus for each person
- **Auto-play Controls**: Toggle automatic translation playback
- **Settings Persistence**: Preferences saved to localStorage

## Browser Compatibility

### Required Features
- Web Speech API (speech recognition)
- Speech Synthesis API (text-to-speech)
- localStorage
- Fetch API

### Supported Browsers
- Chrome/Chromium (recommended)
- Edge
- Safari (limited speech recognition support)
- Firefox (limited speech recognition support)

### Mobile Support
- Chrome Mobile (Android)
- Safari Mobile (iOS - limited)
- Samsung Internet

## Technical Architecture

### Frontend
- **HTML5**: Semantic markup with accessibility features
- **Tailwind CSS**: Utility-first styling with custom components
- **Vanilla JavaScript**: No framework dependencies for optimal performance

### Backend
- **Vercel Serverless Functions**: Node.js API endpoints
- **OpenAI GPT-4**: Conversational translation with context awareness
- **Environment Variables**: Secure credential management

### APIs Used
- **OpenAI Chat Completions**: For intelligent translation
- **Web Speech API**: For speech-to-text conversion
- **Speech Synthesis API**: For text-to-speech playback

## Security Features

- Admin key authentication with session management
- API key protection via serverless functions
- CORS headers and security policies
- Input validation and error handling
- No client-side API key exposure

## Performance Optimizations

- Minimal JavaScript bundle size
- Efficient DOM manipulation
- Lazy loading of speech synthesis voices
- Optimized Tailwind CSS (unused classes purged)
- Conversation context caching

## Accessibility Features

- Screen reader support
- Keyboard navigation
- High contrast mode support
- Focus indicators
- Reduced motion support
- ARIA labels and roles

## Troubleshooting

### Common Issues

1. **Speech Recognition Not Working**
   - Ensure HTTPS connection (required for microphone access)
   - Check browser compatibility
   - Grant microphone permissions

2. **Translation Errors**
   - Verify OpenAI API key is valid and has credits
   - Check network connection
   - Review browser console for errors

3. **Authentication Issues**
   - Verify ADMIN_KEY in environment variables
   - Clear localStorage if needed
   - Check serverless function deployment

### Browser Permissions

The app requires microphone access for speech recognition. Ensure you:
1. Allow microphone access when prompted
2. Use HTTPS (required for microphone API)
3. Check browser settings if permissions are blocked

## License

MIT License - see LICENSE file for details
